<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { get } from 'svelte/store';
	import { getInitials } from '$src/lib/utils/avatarGenerator';
	import { page } from '$app/stores';
	import { onMount, onDestroy } from 'svelte';

	$: lang = get(language);

	import type { Customer } from '$lib/types/customer';
	import type { PollingConfig } from '$lib/types/polling';
	import { PollingService } from '$lib/services/pollingService';
	import {
		Card,
		Button,
		Label,
		Input,
		Checkbox,
		AccordionItem,
		Hr,
		Dropdown,
		DropdownItem,
		Textarea,
		Indicator
	} from 'flowbite-svelte';

	import {
		PlusOutline,
		AngleDownOutline,
		PenOutline,
		TrashBinOutline,
		FilePenOutline,
		SearchOutline
	} from 'flowbite-svelte-icons';

	import { getColorClass, formatTimestamp } from '$lib/utils';
	import {
		formatDateOfBirth,
		getFullNationality,
		formatFullAddress,
		hasValidAddress
	} from '$lib/utils';

	import { getBackendUrl } from '$src/lib/config';

	import NoteEditModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteEditModal.svelte';
	import NoteDeleteModal from '$src/routes/(site)/monitoring/[id]/InfoDisplayPanel/NoteDeleteModal.svelte';
	import CustomerEdit from '$lib/components/UI/CustomerEdit.svelte';
	import CustomerTag from '$src/lib/components/UI/CustomerTag.svelte';

	let editModal = false;
	let deleteModal = false;
	let editSummary;
	let deleteSummaryId;

	export let customer: Customer;

	// console.log('informationtab customer tag:', customer.tags);
	export let platformId: number;
	export let access_token: string;

	// Avatar image error tracking
	let imageErrors = new Set<number>();

	function formatDate(dateString: string | null) {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	}

	// function formatTimestamp(timestamp) {
	// 	const date = new Date(timestamp);
	// 	const options = {
	// 		day: '2-digit',
	// 		month: 'short',
	// 		year: 'numeric',
	// 		hour: '2-digit',
	// 		minute: '2-digit',
	// 		hour12: false
	// 	};

	// 	const locale = $language === 'th' ? 'th-TH' : 'en-US';

	// 	return date.toLocaleString(locale, options).replace(',', '');
	// }

	// function getCustomerTypeColor(type: string) {
	// 	switch (type) {
	// 		case 'VIP': return 'bg-purple-100 text-purple-800';
	// 		case 'REGULAR': return 'bg-blue-100 text-blue-800';
	// 		case 'NEW': return 'bg-green-100 text-green-800';
	// 		default: return 'bg-gray-100 text-gray-800';
	// 	}
	// }

	import { services } from '$src/lib/api/features';

	// Polling service instance
	let pollingService: PollingService;

	let notes: any[] = [];
	let loadingNotes = false;
	let customerTags: any[] = [];
	let loadingTags = false;
	let ownersHistoryticket: any[] = [];
	let loadingHistory = false;

	// Data refresh state variables
	let refreshingProfile = false;
	let refreshingTags = false;

	// Initial data loading state management
	let initialDataLoaded = false;
	let initialDataLoading = false;

	// Current user role
	$: currentUserRole = $page.data.role;

	// Track current customer ID to reset flags when customer changes
	let currentCustomerId: number | null = null;

	// Initialize data loading and polling when customer data becomes available
	// Guard against multiple executions with initialDataLoaded and initialDataLoading flags
	// $: if (customer && customer.customer_id && access_token && !pollingService && !initialDataLoaded && !initialDataLoading) {
	// 	// Reset flags if customer has changed
	// 	if (currentCustomerId !== null && currentCustomerId !== customer.customer_id) {
	// 		console.log('InformationTab.svelte: Customer changed, resetting initialization flags');
	// 		initialDataLoaded = false;
	// 		initialDataLoading = false;
	// 		// Clean up existing polling service
	// 		if (pollingService) {
	// 			pollingService.unregisterEndpoint('customer-profile');
	// 			pollingService.unregisterEndpoint('customer-tags');
	// 			pollingService.unregisterEndpoint('customer-notes');
	// 			pollingService.unregisterEndpoint('user-history');
	// 			pollingService = null;
	// 		}
	// 	}
	// 	currentCustomerId = customer.customer_id;
	// 	initializeDataAndPolling();
	// }

	// Custom fetcher functions for polling service
	async function fetchCustomerProfile() {
		try {
			refreshingProfile = true;
			const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customer.customer_id}/`, {
				credentials: 'include'
			});

			if (response.ok) {
				const freshCustomerData = await response.json();
				// console.log('InformationTab.svelte: fetchCustomerProfile(): Successfully fetched customer data:', freshCustomerData);
				return freshCustomerData;
			} else {
				const errorText = await response.text();
				throw new Error(`InformationTab.svelte: fetchCustomerProfile(): Failed to fetch customer data: ${response.status} - ${errorText}`);
			}
		} catch (error) {
			console.error('InformationTab.svelte: fetchCustomerProfile(): Failed to fetch customer profile:', error);
			throw error;
		} finally {
			refreshingProfile = false;
		}
	}

	async function fetchCustomerTags() {
		try {
			loadingTags = true;
			const response_customer_tag = await services.customers.getFilterTags(access_token);
			return response_customer_tag.data || [];
		} catch (error) {
			console.error('Error fetching customer tags:', error);
			throw error;
		} finally {
			loadingTags = false;
		}
	}

	async function fetchCustomerNotes() {
		try {
			loadingNotes = true;
			const response_customer_notes = await services.customers.getCustomerNotes(
				customer.customer_id,
				access_token
			);
			return response_customer_notes.customer_notes || [];
		} catch (error) {
			console.error('Error fetching customer notes:', error);
			throw error;
		} finally {
			loadingNotes = false;
		}
	}

	async function fetchUserHistory() {
		try {
			loadingHistory = true;

			// First request: Get platform info using customer_id and main_interface_id
			const platformInfo = await services.customers.getPlatformInfo(
				customer.customer_id,
				platformId,
				access_token
			);

			// Second request: Get ticket owners using the platform info id as backend_ticket_id
			const backendTicketId = platformInfo.id;
			const ticketOwnersResponse = await fetch(
				`${getBackendUrl()}/ticket/api/tickets/${backendTicketId}/owners/`,
				{
					headers: {
						Authorization: `Bearer ${access_token}`,
						'Content-Type': 'application/json'
					}
				}
			);

			if (!ticketOwnersResponse.ok) {
				throw new Error('Failed to fetch ticket owners');
			}

			const ticketOwners = await ticketOwnersResponse.json();
			return ticketOwners || [];
		} catch (error) {
			console.error('Error fetching user history:', error);
			throw error;
		} finally {
			loadingHistory = false;
		}
	}

	function openEditModal(note) {
		editModal = true;
		editSummary = { ...note };
	}

	function closeEditModal() {
		editModal = false;
		editSummary = null;
	}

	function openDeleteModal(id) {
		deleteModal = true;
		deleteSummaryId = id;
	}

	let isNotesOpen = true;
	function toggleNotes() {
		isNotesOpen = !isNotesOpen;
	}

	export let loadingOwnerHistory = false;
	let isOwnerHistoryOpen = true;
	function toggleOwnerHistory() {
		isOwnerHistoryOpen = !isOwnerHistoryOpen;
	}

	$: ownerHistory = ownersHistoryticket?.owner_history || [];
	$: currentOwner = ownersHistoryticket?.current_owner;

	function handleImageError(identityId: number) {
		imageErrors.add(identityId);
		imageErrors = imageErrors; // Trigger reactivity
	}

	function isValidImageUrl(url: string | undefined): boolean {
		if (!url) return false;
		// Basic URL validation
		try {
			new URL(url);
			return true;
		} catch {
			return false;
		}
	}

	// Note management state
	let noteContent = '';
	let noteSearchQuery = '';

	// Reactive statements for note functionality
	$: hasNoteContent = noteContent.trim().length > 0;
	$: filteredNotes =
		noteSearchQuery.trim() === ''
			? notes
			: notes.filter((note) => note.content.toLowerCase().includes(noteSearchQuery.toLowerCase()));
	$: displayedNotesCount = noteSearchQuery.trim() === '' ? notes.length : filteredNotes.length;

	// Initial data loading and polling service initialization
	onMount(() => {
		// Only initialize if not already done and not in progress
		if (customer && customer.customer_id && access_token && !initialDataLoaded && !initialDataLoading) {
			initializeDataAndPolling();
		}
	});

	onDestroy(() => {
		if (pollingService) {
			// Clean up all polling endpoints
			pollingService.unregisterEndpoint('customer-profile');
			pollingService.unregisterEndpoint('customer-tags');
			pollingService.unregisterEndpoint('customer-notes');
			pollingService.unregisterEndpoint('user-history');
		}
		// Reset initialization flags for cleanup
		initialDataLoaded = false;
		initialDataLoading = false;
		currentCustomerId = null;
	});

	async function performInitialDataLoad() {
		// Guard against multiple simultaneous executions
		if (initialDataLoading || initialDataLoaded) {
			console.warn('InformationTab.svelte: Initial data load already in progress or completed, skipping...');
			return;
		}

		try {
			initialDataLoading = true;
			// console.log('InformationTab.svelte: Performing initial data load...');

			// Load all data in parallel for faster initial display
			const dataPromises = [
				fetchCustomerProfile().then(data => {
					customer = data;
					// console.log('InformationTab.svelte: Initial customer profile loaded');
				}).catch(error => {
					console.error('InformationTab.svelte: Failed to load initial customer profile:', error);
				}),

				fetchCustomerTags().then(data => {
					customerTags = data;
					// console.log('InformationTab.svelte: Initial customer tags loaded');
				}).catch(error => {
					console.error('InformationTab.svelte: Failed to load initial customer tags:', error);
				}),

				fetchCustomerNotes().then(data => {
					notes = data;
					// console.log('InformationTab.svelte: Initial customer notes loaded');
				}).catch(error => {
					console.error('InformationTab.svelte: Failed to load initial customer notes:', error);
				}),

				fetchUserHistory().then(data => {
					ownersHistoryticket = data;
					// console.log('InformationTab.svelte: Initial user history loaded');
				}).catch(error => {
					console.error('InformationTab.svelte: Failed to load initial user history:', error);
				})
			];

			// Wait for all initial data to load
			await Promise.allSettled(dataPromises);
			// console.log('InformationTab.svelte: Initial data load completed');

		} finally {
			initialDataLoading = false;
			initialDataLoaded = true;
		}
	}

	function initializePolling() {
		pollingService = PollingService.getInstance();

		// Register customer profile polling
		const profileConfig: PollingConfig = {
			interval: 5000, // 5 seconds
			customFetcher: fetchCustomerProfile,
			onDataChange: (data) => {
				customer = data;
			},
			onError: (error) => {
				console.error('Customer profile polling error:', error);
			}
		};
		pollingService.registerEndpoint('customer-profile', profileConfig);

		// Register customer tags polling
		const tagsConfig: PollingConfig = {
			interval: 15000, // 15 seconds
			customFetcher: fetchCustomerTags,
			onDataChange: (data) => {
				customerTags = data;
			},
			onError: (error) => {
				console.error('Customer tags polling error:', error);
			}
		};
		pollingService.registerEndpoint('customer-tags', tagsConfig);

		// Register customer notes polling
		const notesConfig: PollingConfig = {
			interval: 5000, // 5 seconds
			customFetcher: fetchCustomerNotes,
			onDataChange: (data) => {
				notes = data;
			},
			onError: (error) => {
				console.error('Customer notes polling error:', error);
			}
		};
		pollingService.registerEndpoint('customer-notes', notesConfig);

		// Register user history polling
		const historyConfig: PollingConfig = {
			interval: 30000, // 30 seconds
			customFetcher: fetchUserHistory,
			onDataChange: (data) => {
				ownersHistoryticket = data;
			},
			onError: (error) => {
				console.error('User history polling error:', error);
			}
		};
		pollingService.registerEndpoint('user-history', historyConfig);
	}

	async function initializeDataAndPolling() {
		// First, perform initial data load for immediate display
		await performInitialDataLoad();

		// Then initialize polling for subsequent updates
		initializePolling();

		// console.log('InformationTab.svelte: Data loading and polling initialization completed');
	}

	// Manual refresh functions for UI interactions (e.g., after editing)
	async function refreshCustomerProfile() {
		try {
			refreshingProfile = true;
			const freshData = await fetchCustomerProfile();
			customer = freshData;
		} catch (error) {
			console.error('Manual customer profile refresh failed:', error);
		} finally {
			refreshingProfile = false;
		}
	}

	async function refreshCustomerTags() {
		try {
			refreshingTags = true;
			// Refresh customer data to get updated tags
			const freshCustomerData = await fetchCustomerProfile();
			customer = freshCustomerData;
			// Also refresh available tags for the modal
			const freshTags = await fetchCustomerTags();
			customerTags = freshTags;
		} catch (error) {
			console.error('Manual customer tags refresh failed:', error);
		} finally {
			refreshingTags = false;
		}
	}

	// Manual reload function for notes (used after adding/editing/deleting notes)
	async function reloadCustomerNotes() {
		try {
			const freshNotes = await fetchCustomerNotes();
			notes = freshNotes;
		} catch (error) {
			console.error('Failed to reload customer notes:', error);
		}
	}
</script>

<div id="info-tab-information-tab" class="h-full w-full space-y-6 overflow-y-auto p-4" data-testid="information-tab">
	<!-- Customer Profile Card -->
	<div id="info-tab-customer-profile-card" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="customer-profile-card">
		<!-- Customer Header -->
		<div class="text-center mb-4">
			<div
				id="info-tab-customer-avatar"
				class="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 text-2xl font-medium text-gray-600"
				data-testid="customer-avatar"
			>
				<!-- {#if isValidImageUrl(customer.platform_identities[0].picture_url) && !imageErrors.has(customer.name)} -->
				{#if customer.platform_identities && customer.platform_identities.length > 0 && isValidImageUrl(customer.platform_identities[0].picture_url) && !imageErrors.has(customer.name)}
					<img
						src={customer.platform_identities[0].picture_url}
						alt="{getInitials(customer.name)} avatar"
						class="h-full w-full rounded-full object-cover"
						on:error={() => handleImageError(customer.name)}
					/>
				{:else}
					<!-- Fallback initials when no picture_url or image failed to load -->
					{getInitials(customer.name)}
				{/if}
			</div>
			<h2 id="info-tab-customer-name" class="text-xl font-semibold" data-testid="customer-name">{customer.name || 'Unknown Customer'}</h2>
			{#if currentUserRole === 'Admin' || currentUserRole === 'System'}
				<p id="info-tab-customer-id" class="text-xs text-gray-500" data-testid="customer-id">{t('customer_id')}: {customer.customer_id.toString()}</p>
			{/if}
		</div>

		<!-- Basic Information -->
		<div id="info-tab-basic-information-section" data-testid="basic-information-section">
			<div class="flex items-center justify-between">
				<div id="info-tab-basic-information-title" class="text-lg font-medium text-gray-700 flex items-center">
					{t('basic_information')}
					<!-- {#if refreshingProfile}
						<div class="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-500"></div>
					{/if} -->
				</div>

				<CustomerEdit {customer} onRefresh={refreshCustomerProfile} />
			</div>

			<div id="info-tab-basic-information-fields" class="space-y-1">
				<div>
					<label class="text-xs text-gray-500">{t('first_name')}</label>
					<p id="info-tab-first-name" class="text-sm font-medium">
						{customer.first_name ? `${customer.first_name}` : t('not_provided')}
					</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('last_name')}</label>
					<p id="info-tab-last-name" class="text-sm font-medium">
						{customer.last_name ? `${customer.last_name}` : t('not_provided')}
					</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('date_of_birth')}</label>
					<!-- <p class="text-sm font-medium">{customer.date_of_birth || t('not_provided')}</p> -->
					<p class="text-sm font-medium">
						{customer.date_of_birth
							? formatDateOfBirth(customer.date_of_birth, lang)
							: t('not_provided')}
					</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('national_id')}</label>
					<p id="info-tab-national-id" class="text-sm font-medium">{customer.national_id || t('not_provided')}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('nationality')}</label>
					<!-- <p class="text-sm font-medium">{customer.nationality || t('not_provided')}</p> -->
					<p id="info-tab-nationality" class="text-sm font-medium">
						{customer.nationality
							? getFullNationality(customer.nationality, lang)
							: t('not_provided')}
					</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('passport_number')}</label>
					<p id="info-tab-passport-number" class="text-sm font-medium">{customer.passport_number || t('not_provided')}</p>
				</div>

				<!-- <div>
					<label class="text-xs text-gray-500">{t('address')}</label>
					<p class="text-sm font-medium">
						{customer.address.address_line1 || t('not_provided')}
						{#if customer.address.address_line2}
							<br />{customer.address.address_line2}
						{/if}
						{#if customer.address.district || customer.address.province}
							<br />{[customer.address.district, customer.address.province]
								.filter(Boolean)
								.join(', ')}
						{/if}
					</p>
				</div> -->
				<div>
					<label class="text-xs text-gray-500">{t('address')}</label>
					<p id="info-tab-address" class="text-sm font-medium">
						{hasValidAddress(customer.address)
							? formatFullAddress(customer.address, lang)
							: t('not_provided')}
					</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('phone_number')}</label>
					<p id="info-tab-phone-number" class="text-sm font-medium">{customer.phone || t('not_provided')}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('email')}</label>
					<p id="info-tab-email" class="text-sm font-medium">{customer.email || t('not_provided')}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('contact_channel')}</label>
					<p id="info-tab-contact-channel" class="text-sm font-medium">{customer.main_interface_id?.name || 'LINE'}</p>
				</div>

				<div>
					<label class="text-xs text-gray-500">{t('career')}</label>
					<p id="info-tab-career" class="text-sm font-medium">{customer.career || t('not_provided')}</p>
				</div>
			</div>
		</div>

		<!-- Platform Identities -->
		<!-- <div>
			<h3 class="text-sm font-medium text-gray-700 mb-3">Connected Platforms</h3>
			<div class="space-y-2">
				{#if customer.platforms && customer.platforms.length > 0}
					{#each customer.platforms as platform}
						<div class="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
							<div class="flex items-center space-x-2">
								<span class="text-lg">{platform.platform === 'LINE' ? '💚' : '💬'}</span>
								<span class="text-sm font-medium">{platform.platform}</span>
							</div>
							{#if platform.verified}
								<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
								</svg>
							{/if}
						</div>
					{/each}
				{:else}
					<p class="text-sm text-gray-500">No connected platforms</p>
				{/if}
			</div>
		</div>
		 -->
		<!-- Tags -->
		<!-- {#if customer.tags && customer.tags.length > 0}
			<div>
				<h3 class="text-sm font-medium text-gray-700 mb-3">Tags</h3>
				<div class="flex flex-wrap gap-2">
					{#each customer.tags as tag}
						<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
							{tag}
						</span>
					{/each}
				</div>
			</div>
		{/if} -->
	</div>

	<!-- Customer Tags -->
	<div id="info-tab-customer-tags-section" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="customer-tags-section">
		<div class="mb-3 flex items-center justify-between">
			<div id="info-tab-customer-tags-title" class="text-lg font-medium text-gray-700 flex items-center">
				{t('customer_tags')}
				<!-- {#if refreshingTags}
					<div class="ml-2 h-4 w-4 animate-spin rounded-full border-b-2 border-blue-500"></div>
				{/if} -->
			</div>

			<CustomerTag {customer} customer_tags={customerTags} onRefresh={refreshCustomerTags} />
		</div>

		<div id="info-tab-customer-tags-list" class="flex flex-wrap gap-2" data-testid="customer-tags-list">
			{#if customer.tags && customer.tags.length > 0}
				{#each customer.tags as tag}
					<!-- <span class="inline-flex items-center gap-2 rounded-md bg-gray-100 px-3 py-1 text-sm text-gray-700"> -->
					<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm" data-testid="customer-tag">
						<!-- <span class="inline-block w-2 h-2 rounded-full" style="background-color: {tag.color}"></span> -->
						<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
						{tag.name}
					</span>
				{/each}
			{:else}
				<span class="text-sm text-gray-500">{t('no_tags')}</span>
			{/if}
		</div>
	</div>

	<!-- Staff History -->
	<div id="info-tab-staff-history-section" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="staff-history-section">
		<div class="w-full">
			<div
				id="info-tab-staff-history-toggle"
				class="flex cursor-pointer items-center justify-between rounded-lg transition-colors"
				on:click={toggleOwnerHistory}
				on:keydown={(e) => e.key === 'Enter' && toggleOwnerHistory()}
				role="button"
				tabindex="0"
				data-testid="staff-history-toggle"
			>
				<div id="info-tab-staff-history-title" class="text-lg font-medium text-gray-700">{t('staff_history')}</div>
				{#if ownerHistory.length > 0}
					<div class="flex items-center text-sm text-gray-500 hover:bg-gray-100">
						<span>{ownerHistory.length} {t('employee')}</span>
						<AngleDownOutline
							class="ml-1 h-4 w-4 transform transition-transform duration-200 {isOwnerHistoryOpen
								? 'rotate-180'
								: ''}"
						/>
					</div>
				{/if}
			</div>

			{#if isOwnerHistoryOpen}
				<div id="info-tab-staff-history-content" class="mt-4 transition-all duration-300 ease-in-out" data-testid="staff-history-content">
					{#if loadingOwnerHistory}
						<!-- <div class="flex items-center justify-center py-8">
							<div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
							<span class="ml-2 text-sm text-gray-500">{t('loading_owner_history')}</span>
						</div> -->
					{:else if ownerHistory.length > 0}
						<div id="info-tab-staff-history-list" class="space-y-4" data-testid="staff-history-list">
							<!-- Owner History List -->
							{#each ownerHistory as historyItem, index}
								{@const owner = historyItem.owner || historyItem.created_by_user}
								{@const isCurrentOwner = index === 0 || historyItem.is_current}

								<div class="rounded-lg border border-gray-100 bg-white p-3 shadow-sm">
									<div class="flex items-center justify-between">
										<div class="flex items-center space-x-3">
											<!-- Avatar -->
											<!-- <div class="w-12 h-12 rounded-full flex items-center justify-center text-white font-medium {getAvatarColor(owner?.name || owner?.username)}">
												{getInitials(owner?.name || owner?.username)}
											</div> -->

											<!-- Owner Info -->
											<div>
												<div class="font-medium text-gray-900">
													{owner?.name || owner?.username || t('unknown')}
												</div>
												<div class="text-sm text-gray-500">
													{t('role')}: {owner?.roles}
												</div>
												<div class="text-xs text-gray-400">
													{t('assigned_on')}: {formatTimestamp(historyItem.created_on)}
												</div>
												{#if historyItem.note}
													<div class="mt-1 text-xs text-gray-600">
														{t('note')}: {historyItem.note}
													</div>
												{/if}
											</div>
										</div>

										<!-- Current Badge -->
										{#if isCurrentOwner}
											<span
												class="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-700"
											>
												{t('current')}
											</span>
										{/if}
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<div class="py-8 text-center">
							<div class="mb-2 text-gray-400">
								<svg
									class="mx-auto h-12 w-12"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
									/>
								</svg>
							</div>
							<p class="text-gray-500">{t('no_owner_history_available')}</p>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>

	<!-- Notes -->
	<div id="info-tab-notes-section" class="mb-4 w-full rounded-lg bg-white p-4 shadow-md" data-testid="notes-section">
		<div class="w-full">
			<div
				id="info-tab-notes-toggle"
				class="flex cursor-pointer items-center justify-between rounded-lg transition-colors"
				on:click={toggleNotes}
				data-testid="notes-toggle"
			>
				<div id="info-tab-notes-title" class="text-lg font-medium text-gray-700">{t('notes')}</div>
				{#if notes.length > 0}
					<div class="flex items-center text-sm text-gray-500 hover:bg-gray-100">
						<span>{displayedNotesCount} {t('notes')}</span>
						<svg
							class="ml-1 h-4 w-4 transform transition-transform duration-200 {isNotesOpen
								? 'rotate-180'
								: ''}"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M19 9l-7 7-7-7"
							/>
						</svg>
					</div>
				{/if}
			</div>

			{#if isNotesOpen}
				<div id="info-tab-notes-content" class="mt-4 transition-all duration-300 ease-in-out" data-testid="notes-content">
					<!-- <div class="flex items-center justify-center py-8">
						<div class="h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"></div>
						<span class="ml-2 text-sm text-gray-500">{t('loading_notes')}</span>
					</div> -->
					<!-- Add New Note Form - Always visible when notes section is open -->
					<form
						id="info-tab-add-note-form"
						method="POST"
						enctype="multipart/form-data"
						action="?/upload_note"
						data-testid="add-note-form"
						use:enhance={() => {
							return async ({ update, result }) => {
								if (result.type === 'success') {
									await update();
									// Reset note content after successful submission
									noteContent = '';
									// Reload notes after successful creation
									await reloadCustomerNotes();
								}
							};
						}}
						class="mb-4 rounded-lg border bg-white p-4 shadow-sm"
					>
						<input type="hidden" name="customer_id" value={customer.customer_id} />

						<div class="mb-3">
							<Textarea
								id="info-tab-note_content"
								name="content"
								bind:value={noteContent}
								placeholder={t('new_note')}
								rows={3}
								required
								class="mt-1"
							/>
						</div>

						<Button id="info-tab-add-note-button" color="blue" type="submit" class="w-full" disabled={!hasNoteContent} data-testid="add-note-button">
							<PlusOutline class="mr-2 h-4 w-4" />
							{t('add_note')}
						</Button>
					</form>

					<div class="relative mb-4 flex-grow">
						<input
							id="info-tab-search-note"
							type="text"
							bind:value={noteSearchQuery}
							placeholder={t('search_note_placeholder')}
							class="w-full rounded-lg border border-gray-300 px-3 py-2 pl-9 text-sm text-gray-600
						focus:outline-none focus:ring-2 focus:ring-blue-500"
							aria-label="Search notes"
							role="searchbox"
							autocomplete="off"
						/>
						<SearchOutline class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
					</div>

					{#if notes.length > 0}
						<!-- Existing Notes List -->
						<div id="info-tab-notes-list" class="space-y-4" data-testid="notes-list">
							{#if filteredNotes.length === 0}
								<div id="info-tab-no-notes-found" class="py-8 text-center" data-testid="no-notes-found">
									<!-- <FilePenOutline class="mx-auto my-4 h-12 w-12 text-gray-400" /> -->
									<p class="text-gray-500">{t('no_notes_found')}</p>
								</div>
							{:else}
								{#each filteredNotes as note}
									<div id="info-tab-note-{note.id}" class="rounded-lg border border-gray-100 bg-white p-3 shadow-sm space-y-4" data-testid="note-item">
										<div class="text-md whitespace-pre-wrap leading-relaxed text-gray-900">
											{note.content}
										</div>
										<div class="flex items-center justify-between">
											<div class="flex flex-col space-y-1">
												<!-- <span class="text-xs text-gray-700">
													{t('note_created_on')}
													{formatTimestamp(note.created_on)}
													{t('note_created_by')}
													{note.created_by_name}
												</span> -->
												<span class="text-xs text-gray-700">
													{t('note_updated_on')}
													{formatTimestamp(note.updated_on)}
													{t('note_created_by')}
													{note.updated_by_name}
												</span>
											</div>

											<div>
												<Button
													color="light"
													class="flex h-6 w-6 items-center justify-center rounded-full p-2 text-gray-500"
												>
													<AngleDownOutline class="h-4 w-4" />
												</Button>

												<Dropdown>
													<DropdownItem
														id="info-tab-edit-note"
														class="flex items-center space-x-2"
														on:click={openEditModal(note)}
													>
														<PenOutline class="mr-2 h-4 w-4" />
														{t('edit')}
													</DropdownItem>
													<DropdownItem
														id="info-tab-delete-note"
														class="flex items-center space-x-2"
														on:click={openDeleteModal(note.id)}
													>
														<TrashBinOutline class="mr-2 h-4 w-4" />
														{t('delete')}
													</DropdownItem>
												</Dropdown>
											</div>
										</div>
									</div>
								{/each}
							{/if}
						</div>
					{:else}
						<!-- No Notes Message -->
						<div id="info-tab-no-notes-available" class="py-8 text-center" data-testid="no-notes-available">
							<FilePenOutline class="mx-auto my-4 h-12 w-12 text-gray-400" />
							<p class="text-gray-500">{t('no_notes_available')}</p>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>
</div>

<NoteEditModal
	bind:editModal
	editNote={editSummary}
	customerId={customer.customer_id}
	closeModal={closeEditModal}
	onSuccess={reloadCustomerNotes}
/>

<NoteDeleteModal
	bind:deleteModal
	deleteNoteId={deleteSummaryId}
	customerId={customer.customer_id}
	onSuccess={reloadCustomerNotes}
/>
